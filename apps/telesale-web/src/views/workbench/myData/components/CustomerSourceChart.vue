<!--
 * @Date         : 2025-07-21 16:52:28
 * @Description  : 客户来源图表组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import CustomerSourceBarChart from "./chat/CustomerSourceBarChart.vue";
import CustomerSourcePieChart from "./chat/CustomerSourcePieChart.vue";
import TimeFilter from "./TimeFilter.vue";

const time = ref();
const changeTime = () => {
  console.log("time", time.value);
};
</script>

<template>
  <el-card>
    <div class="flex justify-end">
      <TimeFilter v-model="time" @change="changeTime" />
    </div>
    <el-row>
      <el-col :span="12">
        <CustomerSourceBarChart />
      </el-col>
      <el-col :span="12">
        <CustomerSourcePieChart />
      </el-col>
    </el-row>
  </el-card>
</template>

<style lang="scss" scoped></style>

<!--
 * @Date         : 2025-07-21 16:52:28
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import PerformanceChart from "./chat/PerformanceChart.vue";
import PerformancePieChart from "./chat/PerformancePieChart.vue";
import TimeFilter from "./TimeFilter.vue";

const time = ref();
const changeTime = () => {
  console.log("time", time.value);
};
</script>

<template>
  <el-card>
    <div class="flex justify-end">
      <TimeFilter v-model="time" @change="changeTime" />
    </div>
    <el-row>
      <el-col :span="14">
        <PerformanceChart />
      </el-col>
      <el-col :span="10">
        <PerformancePieChart />
      </el-col>
    </el-row>
  </el-card>
</template>

<style lang="scss" scoped></style>

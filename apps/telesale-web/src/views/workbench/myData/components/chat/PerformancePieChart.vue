<!--
 * @Date         : 2025-07-18
 * @Description  : 业绩分类饼图
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { ECOption } from "/@/components/Echarts/config/index";
import Echarts from "/@/components/Echarts/index.vue";

interface PieData {
  name: string;
  value: number;
  percentage?: number;
}

interface PerformancePieChartProps {
  loading?: boolean;
  data?: PieData[];
  title?: string;
}

const props = withDefaults(defineProps<PerformancePieChartProps>(), {
  loading: false,
  data: () => [],
  title: "业绩分类"
});

// 模拟数据（实际使用时从props.data获取）
const mockData = [
  { name: "一年制头条", value: 35, percentage: 35 },
  { name: "升学", value: 20, percentage: 20 },
  { name: "升学提升课程", value: 15, percentage: 15 },
  { name: "短期高效", value: 15, percentage: 15 },
  { name: "其他综合套餐", value: 15, percentage: 15 }
];

// 使用传入的数据或模拟数据
const chartData = computed(() => {
  const data = props.data.length > 0 ? props.data : mockData;
  // 计算百分比
  const total = data.reduce((sum, item) => sum + item.value, 0);
  return data.map(item => ({
    ...item,
    percentage: total > 0 ? Math.round((item.value / total) * 100) : 0
  }));
});

// 颜色配置
const colors = [
  "#1890ff",
  "#52c41a",
  "#faad14",
  "#f5222d",
  "#722ed1",
  "#13c2c2",
  "#eb2f96"
];

// ECharts配置
const option = computed<ECOption>(() => {
  return {
    title: {
      text: props.title,
      left: "left",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
        color: "#333"
      }
    },
    tooltip: {
      trigger: "item",
      formatter: (params: any) => {
        return `${params.name}<br/>数量: ${params.value}<br/>占比: ${params.percent}%`;
      }
    },
    legend: {
      orient: "vertical",
      right: "10%",
      top: "middle",
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        fontSize: 12
      },
      formatter: (name: string) => {
        const item = chartData.value.find(d => d.name === name);
        return `${name} ${item?.percentage || 0}%`;
      }
    },
    color: colors,
    series: [
      {
        name: "业绩分类",
        type: "pie",
        radius: ["40%", "70%"],
        center: ["35%", "50%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: "#fff",
          borderWidth: 2
        },
        label: {
          show: false,
          position: "center"
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: "bold",
            formatter: (params: any) => {
              return `${params.name}\n${params.percent}%`;
            }
          }
        },
        labelLine: {
          show: false
        },
        data: chartData.value.map(item => ({
          name: item.name,
          value: item.value
        }))
      }
    ]
  };
});
</script>

<template>
  <Echarts :option="option" height="400" />
</template>
